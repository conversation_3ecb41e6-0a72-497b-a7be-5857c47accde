﻿<Window x:Class="SimpleRentalApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تدبير الكراء - نظام إدارة المحلات والإيجارات"
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F8F9FA"
        WindowStyle="None"
        AllowsTransparency="True"
        WindowState="Maximized"
        >

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Navigation Button Style -->
        <Style x:Key="NavButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#546E7A"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Margin="5,2"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                                <Setter Property="Foreground" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="24"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0"
                                    Direction="270"
                                    ShadowDepth="4"
                                    BlurRadius="12"
                                    Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="12" Margin="8">
        <Border.Effect>
            <DropShadowEffect Color="#424242"
                            Direction="270"
                            ShadowDepth="8"
                            BlurRadius="20"
                            Opacity="0.2"/>
        </Border.Effect>

        <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

            <!-- Modern Header -->
            <Border Grid.Row="0"
                    Background="White"
                    CornerRadius="12,12,0,0"
                    Padding="30,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- App Icon and Title -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Border Background="#2196F3"
                                CornerRadius="12"
                                Width="48"
                                Height="48"
                                Margin="0,0,15,0">
                            <TextBlock Text="🏠"
                                     FontSize="24"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"/>
                        </Border>
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="تدبير الكراء"
                                     FontSize="24"
                                     FontWeight="Bold"
                                     Foreground="#1A237E"/>
                            <TextBlock Text="نظام إدارة الإيجارات المتقدم"
                                     FontSize="12"
                                     Foreground="#78909C"/>
                        </StackPanel>


                    </StackPanel>



                    <!-- User Info and Sync Status -->
                    <StackPanel Grid.Column="2"
                              Orientation="Horizontal"
                              VerticalAlignment="Center"
                              Margin="0,0,20,0">
                        <!-- تم إزالة مؤشر حالة المزامنة -->



                        <!-- User Info -->
                        <Border Background="#E8F5E8"
                                CornerRadius="20"
                                Padding="12,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="👤" FontSize="16" Margin="0,0,8,0"/>
                                <TextBlock Name="UserInfoTextBlock"
                                         Text="مرحباً، مستخدم"
                                         FontSize="14"
                                         FontWeight="Medium"
                                         Foreground="#2E7D32"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <!-- Window Controls -->
                    <StackPanel Grid.Column="3"
                              Orientation="Horizontal"
                              VerticalAlignment="Center">



                        <Button Name="LogoutButton"
                                Content="🚪 خروج"
                                Background="{StaticResource ErrorBrush}"
                                Foreground="White"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Margin="0,0,10,0"
                                Click="LogoutButton_Click"/>
                        <Button Name="CloseButton"
                                Content="✕"
                                Background="#FF5722"
                                Foreground="White"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Width="40"
                                Height="40"
                                Padding="0"
                                Click="CloseButton_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Modern Navigation -->
            <Border Grid.Row="1"
                    Background="#FAFAFA"
                    Padding="30,15"
                    BorderBrush="#E0E0E0"
                    BorderThickness="0,1,0,1">
                <ScrollViewer HorizontalScrollBarVisibility="Auto"
                            VerticalScrollBarVisibility="Disabled"
                            PanningMode="HorizontalOnly"
                            CanContentScroll="True">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Name="DashboardBtn"
                                Content="🏠 لوحة التحكم"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Background="#E3F2FD"
                                Foreground="{StaticResource PrimaryDarkBrush}"
                                BorderBrush="{StaticResource PrimaryBrush}"
                                Margin="8,4"
                                Click="DashboardBtn_Click"/>
                        <Button Name="CustomersBtn"
                                Content="👥 المكترين"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="8,4"
                                Click="CustomersBtn_Click"/>
                        <Button Name="PropertiesBtn"
                                Content="🏢 المحلات"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="8,4"
                                Click="PropertiesBtn_Click"/>
                        <Button Name="PaymentsManagementBtn"
                                Content="💰 إدارة الأداءات"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="8,4"
                                Click="PaymentsManagementBtn_Click"/>
                        <Button Name="ContractsManagementBtn"
                                Content="📋 إدارة العقود"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="8,4"
                                Click="ContractsManagementBtn_Click"/>
                        <Button Name="CleaningTaxManagementBtn"
                                Content="🧹 إدارة ضريبة النظافة"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="8,4"
                                Click="CleaningTaxManagementBtn_Click"/>
                        <Button Name="WhatsAppNotificationsBtn"
                                Content="📱 نظام إشعارات الواتساب"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="8,4"
                                Click="WhatsAppNotificationsBtn_Click"/>
                        <Button Name="ReportsBtn"
                                Content="📊 التقارير"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="8,4"
                                Click="ReportsBtn_Click"/>
                        <Button Name="DataManagementBtn"
                                Content="📁 إدارة البيانات"
                                Style="{StaticResource SecondaryButtonStyle}"
                                Margin="8,4"
                                Click="DataManagementBtn_Click"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Modern Content Area -->
            <Border Grid.Row="2"
                    Style="{StaticResource CardStyle}"
                    Margin="30,15,30,5">
                <ScrollViewer Name="ContentArea"
                            Background="Transparent"
                            VerticalScrollBarVisibility="Auto"
                            HorizontalScrollBarVisibility="Disabled"
                            PanningMode="VerticalOnly"
                            CanContentScroll="True">
                    <!-- Content will be loaded here dynamically -->
                </ScrollViewer>
            </Border>

            <!-- Developer Credit -->
            <Border Grid.Row="3"
                    Background="#F8F9FA"
                    CornerRadius="8"
                    Margin="30,5,30,20"
                    Padding="15,8">
                <TextBlock Text="هذا البرنامج تم تطويره من طرف حفيظ عبدو"
                           FontSize="11"
                           Foreground="#666666"
                           HorizontalAlignment="Center"
                           FontStyle="Italic"/>
            </Border>
        </Grid>
    </Border>
</Window>
