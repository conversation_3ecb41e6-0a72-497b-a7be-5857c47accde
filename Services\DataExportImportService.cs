using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using SimpleRentalApp.Data;
using SimpleRentalApp.Models;

namespace SimpleRentalApp.Services
{
    /// <summary>
    /// خدمة تصدير واستيراد البيانات
    /// </summary>
    public class DataExportImportService
    {
        private readonly RentalDbContext _context;
        private readonly string _exportDirectory;

        public DataExportImportService(RentalDbContext context)
        {
            _context = context;

            // إنشاء مجلد التصدير في Documents
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            _exportDirectory = Path.Combine(documentsPath, "SimpleRentalApp", "exports");

            if (!Directory.Exists(_exportDirectory))
            {
                Directory.CreateDirectory(_exportDirectory);
            }

            // التأكد من وجود قاعدة البيانات
            Task.Run(async () => await EnsureDatabaseExistsAsync());
        }

        public DataExportImportService()
        {
            _context = new RentalDbContext();

            // إنشاء مجلد التصدير في Documents
            var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            _exportDirectory = Path.Combine(documentsPath, "SimpleRentalApp", "exports");

            if (!Directory.Exists(_exportDirectory))
            {
                Directory.CreateDirectory(_exportDirectory);
            }

            // التأكد من وجود قاعدة البيانات
            Task.Run(async () => await EnsureDatabaseExistsAsync());
        }

        /// <summary>
        /// تصدير جميع البيانات إلى ملف JSON
        /// </summary>
        public async Task<ExportResult> ExportAllDataAsync(string fileName = null)
        {
            try
            {
                // إنشاء اسم الملف إذا لم يتم تحديده
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"rental_data_backup_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.json";
                }

                var filePath = Path.Combine(_exportDirectory, fileName);

                // جمع جميع البيانات بدون المراجع الدائرية
                var exportData = new ExportDataModel
                {
                    ExportDate = DateTime.Now,
                    Version = "1.0",
                    Customers = await _context.Customers
                        .Select(c => new Customer
                        {
                            Id = c.Id,
                            FirstName = c.FirstName,
                            LastName = c.LastName,
                            Phone = c.Phone,
                            Email = c.Email,
                            Address = c.Address,
                            NationalId = c.NationalId,
                            CreatedDate = c.CreatedDate
                        }).ToListAsync(),
                    Properties = await _context.Properties
                        .Select(p => new Property
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Address = p.Address,
                            Type = p.Type,
                            MonthlyRent = p.MonthlyRent,
                            Status = p.Status,
                            CustomerId = p.CustomerId,
                            CleaningTaxPaymentOption = p.CleaningTaxPaymentOption,
                            Rooms = p.Rooms,
                            Bathrooms = p.Bathrooms,
                            Area = p.Area,
                            Description = p.Description,
                            CreatedDate = p.CreatedDate,
                            UpdatedDate = p.UpdatedDate
                        }).ToListAsync(),
                    Contracts = await _context.Contracts
                        .Select(c => new Contract
                        {
                            Id = c.Id,
                            CustomerId = c.CustomerId,
                            PropertyId = c.PropertyId,
                            StartDate = c.StartDate,
                            EndDate = c.EndDate,
                            InitialRentAmount = c.InitialRentAmount,
                            RentIncreaseCount = c.RentIncreaseCount,
                            RentIncreasePercentage = c.RentIncreasePercentage,
                            Notes = c.Notes,
                            CreatedDate = c.CreatedDate,
                            IsActive = c.IsActive
                        }).ToListAsync(),
                    Payments = await _context.Payments
                        .Select(p => new Payment
                        {
                            Id = p.Id,
                            PropertyId = p.PropertyId,
                            CustomerId = p.CustomerId,
                            Type = p.Type,
                            RentAmount = p.RentAmount,
                            CleaningTaxAmount = p.CleaningTaxAmount,
                            DueDate = p.DueDate,
                            PaymentDate = p.PaymentDate,
                            Status = p.Status,
                            PaymentMethod = p.PaymentMethod,
                            CreatedDate = p.CreatedDate
                        }).ToListAsync()
                };

                // تحويل إلى JSON مع تنسيق جميل وتجنب المراجع الدائرية
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                    ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles,
                    MaxDepth = 32
                };

                var jsonString = JsonSerializer.Serialize(exportData, options);
                await File.WriteAllTextAsync(filePath, jsonString, System.Text.Encoding.UTF8);

                return new ExportResult
                {
                    Success = true,
                    FilePath = filePath,
                    RecordCount = exportData.Customers.Count + exportData.Properties.Count +
                                exportData.Contracts.Count + exportData.Payments.Count,
                    Message = $"تم تصدير البيانات بنجاح إلى:\n{filePath}"
                };
            }
            catch (Exception ex)
            {
                return new ExportResult
                {
                    Success = false,
                    Message = $"فشل في تصدير البيانات: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// تصدير قاعدة البيانات SQLite كاملة
        /// </summary>
        public async Task<ExportResult> ExportDatabaseAsync(string fileName = null)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName))
                {
                    fileName = $"rental_database_backup_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.db";
                }

                var destinationPath = Path.Combine(_exportDirectory, fileName);
                var sourcePath = FindDatabaseFile();

                // التحقق من وجود ملف قاعدة البيانات
                if (!File.Exists(sourcePath))
                {
                    // محاولة إنشاء قاعدة البيانات
                    await EnsureDatabaseExistsAsync();

                    // البحث مرة أخرى
                    sourcePath = FindDatabaseFile();

                    if (!File.Exists(sourcePath))
                    {
                        return new ExportResult
                        {
                            Success = false,
                            Message = $"لم يتم العثور على ملف قاعدة البيانات.\n\nالمسارات المفحوصة:\n• {sourcePath}\n• {Directory.GetCurrentDirectory()}\n• {AppDomain.CurrentDomain.BaseDirectory}\n\nيرجى التأكد من تشغيل التطبيق وإنشاء البيانات أولاً."
                        };
                    }
                }

                // إغلاق جميع الاتصالات بقاعدة البيانات قبل النسخ
                await _context.Database.CloseConnectionAsync();

                // انتظار قصير للتأكد من إغلاق الاتصالات
                await Task.Delay(500);

                // نسخ ملف قاعدة البيانات
                File.Copy(sourcePath, destinationPath, true);

                var fileInfo = new FileInfo(destinationPath);

                return new ExportResult
                {
                    Success = true,
                    FilePath = destinationPath,
                    FileSize = fileInfo.Length,
                    Message = $"تم تصدير قاعدة البيانات بنجاح إلى:\n{destinationPath}"
                };
            }
            catch (Exception ex)
            {
                return new ExportResult
                {
                    Success = false,
                    Message = $"فشل في تصدير قاعدة البيانات: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// استيراد البيانات من ملف JSON
        /// </summary>
        public async Task<ImportResult> ImportDataAsync(string filePath, bool replaceExisting = false)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = "الملف المحدد غير موجود"
                    };
                }

                var jsonString = await File.ReadAllTextAsync(filePath, System.Text.Encoding.UTF8);
                var importData = JsonSerializer.Deserialize<ExportDataModel>(jsonString);

                if (importData == null)
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = "فشل في قراءة بيانات الملف"
                    };
                }

                // التحقق من صحة البنية
                var validationResult = ValidateImportData(importData);
                if (!validationResult.IsValid)
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = $"بنية الملف غير صحيحة: {validationResult.ErrorMessage}"
                    };
                }

                // بدء المعاملة
                using var transaction = await _context.Database.BeginTransactionAsync();
                
                try
                {
                    var importedCount = 0;

                    // حذف البيانات الموجودة إذا طُلب ذلك
                    if (replaceExisting)
                    {
                        await ClearAllDataAsync();
                    }

                    // استيراد العملاء
                    if (importData.Customers?.Any() == true)
                    {
                        foreach (var customer in importData.Customers)
                        {
                            if (!replaceExisting && await _context.Customers.AnyAsync(c => c.Id == customer.Id))
                                continue;

                            _context.Customers.Add(customer);
                            importedCount++;
                        }
                    }

                    // استيراد العقارات
                    if (importData.Properties?.Any() == true)
                    {
                        foreach (var property in importData.Properties)
                        {
                            if (!replaceExisting && await _context.Properties.AnyAsync(p => p.Id == property.Id))
                                continue;

                            _context.Properties.Add(property);
                            importedCount++;
                        }
                    }

                    // استيراد العقود
                    if (importData.Contracts?.Any() == true)
                    {
                        foreach (var contract in importData.Contracts)
                        {
                            if (!replaceExisting && await _context.Contracts.AnyAsync(c => c.Id == contract.Id))
                                continue;

                            _context.Contracts.Add(contract);
                            importedCount++;
                        }
                    }

                    // استيراد المدفوعات
                    if (importData.Payments?.Any() == true)
                    {
                        foreach (var payment in importData.Payments)
                        {
                            if (!replaceExisting && await _context.Payments.AnyAsync(p => p.Id == payment.Id))
                                continue;

                            _context.Payments.Add(payment);
                            importedCount++;
                        }
                    }

                    // تم إزالة استيراد مدفوعات ضريبة النظافة

                    await _context.SaveChangesAsync();
                    await transaction.CommitAsync();

                    return new ImportResult
                    {
                        Success = true,
                        ImportedRecords = importedCount,
                        Message = $"تم استيراد {importedCount} سجل بنجاح من:\n{Path.GetFileName(filePath)}"
                    };
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw new Exception($"فشل في حفظ البيانات: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                return new ImportResult
                {
                    Success = false,
                    Message = $"فشل في استيراد البيانات: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// استيراد قاعدة بيانات SQLite
        /// </summary>
        public async Task<ImportResult> ImportDatabaseAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = "ملف قاعدة البيانات المحدد غير موجود"
                    };
                }

                // التحقق من صحة ملف قاعدة البيانات
                if (!await ValidateDatabaseFileAsync(filePath))
                {
                    return new ImportResult
                    {
                        Success = false,
                        Message = "ملف قاعدة البيانات غير صحيح أو تالف"
                    };
                }

                // إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                var backupResult = await ExportDatabaseAsync($"backup_before_import_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.db");
                
                // إغلاق الاتصال الحالي
                await _context.Database.CloseConnectionAsync();

                // استبدال قاعدة البيانات
                var currentDbPath = DatabaseService.Instance.GetDatabasePath();
                File.Copy(filePath, currentDbPath, true);

                // إعادة تهيئة قاعدة البيانات
                await DatabaseService.Instance.InitializeDatabaseAsync();

                return new ImportResult
                {
                    Success = true,
                    Message = $"تم استيراد قاعدة البيانات بنجاح من:\n{Path.GetFileName(filePath)}\n\nتم إنشاء نسخة احتياطية في:\n{backupResult.FilePath}"
                };
            }
            catch (Exception ex)
            {
                return new ImportResult
                {
                    Success = false,
                    Message = $"فشل في استيراد قاعدة البيانات: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// التحقق من صحة بيانات الاستيراد
        /// </summary>
        private ValidationResult ValidateImportData(ExportDataModel data)
        {
            if (data == null)
                return new ValidationResult { IsValid = false, ErrorMessage = "البيانات فارغة" };

            if (string.IsNullOrEmpty(data.Version))
                return new ValidationResult { IsValid = false, ErrorMessage = "إصدار البيانات غير محدد" };

            // يمكن إضافة المزيد من التحققات هنا

            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// التحقق من صحة ملف قاعدة البيانات
        /// </summary>
        private async Task<bool> ValidateDatabaseFileAsync(string filePath)
        {
            try
            {
                var connectionString = $"Data Source={filePath}";
                var options = new DbContextOptionsBuilder<RentalDbContext>()
                    .UseSqlite(connectionString)
                    .Options;
                using var context = new RentalDbContext();
                
                // محاولة الاتصال والتحقق من وجود الجداول الأساسية
                await context.Database.OpenConnectionAsync();
                
                var tables = new[] { "Customers", "Properties", "Contracts", "Payments" };
                foreach (var table in tables)
                {
                    var command = context.Database.GetDbConnection().CreateCommand();
                    command.CommandText = $"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'";
                    var result = await command.ExecuteScalarAsync();
                    
                    if (result == null)
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حذف جميع البيانات
        /// </summary>
        private async Task ClearAllDataAsync()
        {
            _context.Payments.RemoveRange(_context.Payments);
            _context.Contracts.RemoveRange(_context.Contracts);
            _context.Properties.RemoveRange(_context.Properties);
            _context.Customers.RemoveRange(_context.Customers);

            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// تصدير الأداءات إلى ملف Excel
        /// </summary>
        public async Task<ExportResult> ExportPaymentsToExcelAsync(List<Payment> payments, List<Property> properties, List<Customer> customers)
        {
            try
            {
                var fileName = $"payments_export_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.csv";
                var filePath = Path.Combine(_exportDirectory, fileName);

                var csvContent = new StringBuilder();

                // Headers
                csvContent.AppendLine("رقم الدفعة,المحل,المكتري,نوع الدفعة,مبلغ الإيجار,ضريبة النظافة,المبلغ الإجمالي,تاريخ الاستحقاق,تاريخ الدفع,الحالة,طريقة الدفع,ملاحظات");

                // Data rows
                foreach (var payment in payments)
                {
                    var property = properties.FirstOrDefault(p => p.Id == payment.PropertyId);
                    var customer = customers.FirstOrDefault(c => c.Id == payment.CustomerId);

                    var row = $"{payment.Id}," +
                             $"\"{property?.Name ?? "غير محدد"}\"," +
                             $"\"{customer?.FullName ?? "غير محدد"}\"," +
                             $"\"{payment.TypeDisplay}\"," +
                             $"{payment.RentAmount}," +
                             $"{payment.CleaningTaxAmount}," +
                             $"{payment.TotalAmount}," +
                             $"{payment.DueDate:yyyy-MM-dd}," +
                             $"{payment.PaymentDate?.ToString("yyyy-MM-dd") ?? "غير محدد"}," +
                             $"\"{payment.StatusDisplay}\"," +
                             $"\"{payment.PaymentMethod}\"," +
                             $"\"{payment.Notes}\"";

                    csvContent.AppendLine(row);
                }

                await File.WriteAllTextAsync(filePath, csvContent.ToString(), System.Text.Encoding.UTF8);

                return new ExportResult
                {
                    Success = true,
                    FilePath = filePath,
                    RecordCount = payments.Count,
                    Message = $"تم تصدير {payments.Count} دفعة بنجاح إلى:\n{filePath}"
                };
            }
            catch (Exception ex)
            {
                return new ExportResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Message = $"فشل في تصدير الأداءات: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// تصدير توصيلات الكراء إلى ملف Excel
        /// </summary>
        public async Task<ExportResult> ExportReceiptsToExcelAsync(List<RentReceipt> receipts, Property property)
        {
            try
            {
                var fileName = $"receipts_{property.Name}_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.csv";
                var filePath = Path.Combine(_exportDirectory, fileName);

                var csvContent = new StringBuilder();

                // Headers
                csvContent.AppendLine("رقم التوصيل,التاريخ,المبلغ,الشهر المؤدى,الملاحظات,حالة الطباعة");

                // Data rows
                foreach (var receipt in receipts)
                {
                    var row = $"\"{receipt.ReceiptNumber}\"," +
                             $"{receipt.PaymentDate:yyyy-MM-dd}," +
                             $"{receipt.Amount}," +
                             $"\"{receipt.PaymentPeriod}\"," +
                             $"\"{receipt.Notes}\"," +
                             $"\"{(receipt.IsPrinted ? "مطبوع" : "غير مطبوع")}\"";

                    csvContent.AppendLine(row);
                }

                await File.WriteAllTextAsync(filePath, csvContent.ToString(), System.Text.Encoding.UTF8);

                return new ExportResult
                {
                    Success = true,
                    FilePath = filePath,
                    RecordCount = receipts.Count,
                    Message = $"تم تصدير {receipts.Count} توصيل بنجاح إلى:\n{filePath}"
                };
            }
            catch (Exception ex)
            {
                return new ExportResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Message = $"فشل في تصدير التوصيلات: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// الحصول على مجلد التصدير
        /// </summary>
        public string GetExportDirectory() => _exportDirectory;

        /// <summary>
        /// التأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
        /// </summary>
        private async Task EnsureDatabaseExistsAsync()
        {
            try
            {
                await _context.Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن ملف قاعدة البيانات في المواقع المحتملة
        /// </summary>
        private string FindDatabaseFile()
        {
            var possiblePaths = new[]
            {
                "rental_management.db", // المجلد الحالي
                Path.Combine(Directory.GetCurrentDirectory(), "rental_management.db"),
                Path.Combine(Directory.GetCurrentDirectory(), "bin", "Debug", "net9.0-windows", "rental_management.db"),
                Path.Combine(Directory.GetCurrentDirectory(), "bin", "Release", "net9.0-windows", "rental_management.db"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "rental_management.db"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SimpleRentalApp", "rental_management.db")
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                {
                    return path;
                }
            }

            // إذا لم توجد، أرجع المسار الافتراضي
            return Path.Combine(Directory.GetCurrentDirectory(), "rental_management.db");
        }
    }

    /// <summary>
    /// نموذج بيانات التصدير
    /// </summary>
    public class ExportDataModel
    {
        public DateTime ExportDate { get; set; }
        public string Version { get; set; } = "1.0";
        public List<Customer> Customers { get; set; } = new();
        public List<Property> Properties { get; set; } = new();
        public List<Contract> Contracts { get; set; } = new();
        public List<Payment> Payments { get; set; } = new();
    }

    /// <summary>
    /// نتيجة عملية التصدير
    /// </summary>
    public class ExportResult
    {
        public bool Success { get; set; }
        public string FilePath { get; set; } = "";
        public long FileSize { get; set; }
        public int RecordCount { get; set; }
        public string Message { get; set; } = "";
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// نتيجة عملية الاستيراد
    /// </summary>
    public class ImportResult
    {
        public bool Success { get; set; }
        public int ImportedRecords { get; set; }
        public string Message { get; set; } = "";
    }

    /// <summary>
    /// نتيجة التحقق من صحة البيانات
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = "";
    }
}
