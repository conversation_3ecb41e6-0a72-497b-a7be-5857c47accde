using SimpleRentalApp.Models;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;

namespace SimpleRentalApp.Services
{
    public class ReceiptPrintService
    {
        public class PrintResult
        {
            public bool Success { get; set; }
            public string? FilePath { get; set; }
            public string? ErrorMessage { get; set; }
        }

        public async Task<PrintResult> GenerateReceiptAsync(Payment payment, Property property, Customer? customer)
        {
            try
            {
                // Create a RentReceipt from Payment data
                var receipt = new RentReceipt
                {
                    Id = payment.Id,
                    Amount = payment.RentAmount,
                    PaymentDate = payment.PaymentDate ?? DateTime.Now,
                    PaymentPeriod = $"شهر {payment.DueDate:MMMM yyyy}",
                    PeriodStartDate = payment.DueDate.AddDays(-30),
                    PeriodEndDate = payment.DueDate,
                    ReceiptNumber = payment.ReceiptNumber ?? GenerateReceiptNumber(property.Id),
                    Notes = payment.Notes,
                    IsPrinted = false,
                    CreatedDate = DateTime.Now
                };

                // Use ModernPrintService to generate PDF
                var documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                var receiptsDir = Path.Combine(documentsPath, "TadbirAlKira_Receipts");

                if (!Directory.Exists(receiptsDir))
                {
                    Directory.CreateDirectory(receiptsDir);
                }

                var fileName = $"Receipt_{receipt.ReceiptNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
                var filePath = Path.Combine(receiptsDir, fileName);

                // Generate PDF using ModernPrintService
                await Task.Run(() =>
                {
                    try
                    {
                        var printService = ModernPrintService.Instance;
                        var success = printService.SaveReceiptAsPdf(receipt, filePath);

                        if (!success)
                        {
                            throw new Exception("فشل في إنشاء ملف PDF");
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"خطأ في إنشاء PDF: {ex.Message}");
                    }
                });

                return new PrintResult
                {
                    Success = true,
                    FilePath = filePath
                };
            }
            catch (Exception ex)
            {
                return new PrintResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<PrintResult> PrintReceiptAsync(Payment payment, Property property, Customer? customer)
        {
            try
            {
                // Create a RentReceipt from Payment data
                var receipt = new RentReceipt
                {
                    Id = payment.Id,
                    Amount = payment.RentAmount,
                    PaymentDate = payment.PaymentDate ?? DateTime.Now,
                    PaymentPeriod = $"شهر {payment.DueDate:MMMM yyyy}",
                    PeriodStartDate = payment.DueDate.AddDays(-30),
                    PeriodEndDate = payment.DueDate,
                    ReceiptNumber = payment.ReceiptNumber ?? GenerateReceiptNumber(property.Id),
                    Notes = payment.Notes,
                    IsPrinted = false,
                    CreatedDate = DateTime.Now
                };

                // Use ModernPrintService to print
                var printService = ModernPrintService.Instance;
                var success = printService.PrintReceiptWithPreview(receipt);

                if (success)
                {
                    // Mark payment as having receipt printed
                    payment.ReceiptNumber = receipt.ReceiptNumber;
                    await DatabaseService.Instance.UpdatePaymentAsync(payment);
                }

                return new PrintResult
                {
                    Success = success,
                    ErrorMessage = success ? null : "تم إلغاء الطباعة"
                };
            }
            catch (Exception ex)
            {
                return new PrintResult
                {
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private string GenerateReceiptNumber(int propertyId)
        {
            var year = DateTime.Now.Year;
            var timestamp = DateTime.Now.ToString("MMddHHmmss");
            return $"R{year}-{propertyId:D3}-{timestamp}";
        }
    }
}
