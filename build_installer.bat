@echo off
echo Creating Rental Management Installer...
echo.

REM Check for NSIS
set NSIS_PATH=""
if exist "C:\Program Files (x86)\NSIS\makensis.exe" (
    set "NSIS_PATH=C:\Program Files (x86)\NSIS\makensis.exe"
) else if exist "C:\Program Files\NSIS\makensis.exe" (
    set "NSIS_PATH=C:\Program Files\NSIS\makensis.exe"
) else (
    echo ERROR: NSIS not found!
    echo Please download and install NSIS from:
    echo https://nsis.sourceforge.io/Download
    pause
    exit /b 1
)

echo Found NSIS at: %NSIS_PATH%

REM Check required files
if not exist "installer.nsi" (
    echo ERROR: installer.nsi not found!
    pause
    exit /b 1
)

if not exist "RentalManagement_Release_v1.0\Portable\SimpleRentalApp.exe" (
    echo ERROR: Application executable not found!
    pause
    exit /b 1
)

if not exist "app_icon.ico" (
    echo ERROR: Icon file not found!
    pause
    exit /b 1
)

echo All files found. Building installer...
echo.

REM Build installer
"%NSIS_PATH%" installer.nsi

if %ERRORLEVEL% EQU 0 (
    echo.
    echo SUCCESS: Installer created successfully!
    echo File: TadbirAlKira_Setup_v1.0.0.exe
    echo.
    if exist "TadbirAlKira_Setup_v1.0.0.exe" (
        for %%A in (TadbirAlKira_Setup_v1.0.0.exe) do echo Size: %%~zA bytes
    )
) else (
    echo.
    echo ERROR: Failed to create installer!
)

echo.
pause
