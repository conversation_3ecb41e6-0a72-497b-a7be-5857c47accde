@echo off
echo ========================================
echo    Rental Management System Files Check
echo ========================================
echo.

echo [1/4] Checking installer file...
if exist "TadbirAlKira_Setup_v1.0.0.exe" (
    echo ✓ Installer found: TadbirAlKira_Setup_v1.0.0.exe
    for %%A in (TadbirAlKira_Setup_v1.0.0.exe) do echo   Size: %%~zA bytes
) else (
    echo ✗ Installer NOT found!
)

echo.
echo [2/4] Checking portable version...
if exist "RentalManagement_Release_v1.0_ICON_FIXED_2025-07-18.zip" (
    echo ✓ Portable version found
    for %%A in (RentalManagement_Release_v1.0_ICON_FIXED_2025-07-18.zip) do echo   Size: %%~zA bytes
) else (
    echo ✗ Portable version NOT found!
)

echo.
echo [3/4] Checking installer package...
if exist "TadbirAlKira_NSIS_Installer_v1.0.0_2025-07-18.zip" (
    echo ✓ Installer package found
    for %%A in (TadbirAlKira_NSIS_Installer_v1.0.0_2025-07-18.zip) do echo   Size: %%~zA bytes
) else (
    echo ✗ Installer package NOT found!
)

echo.
echo [4/4] Checking documentation...
if exist "RELEASE_SUMMARY.txt" (
    echo ✓ Release summary found
) else (
    echo ✗ Release summary NOT found!
)

if exist "INSTALLER_README.txt" (
    echo ✓ Installer readme found
) else (
    echo ✗ Installer readme NOT found!
)

echo.
echo ========================================
echo Summary:
echo.
echo Available files:
dir /b *.exe *.zip *.txt 2>nul | findstr /v "create_installer\|build_installer\|test_installer\|check_files"
echo.
echo Default login credentials:
echo Username: hafid
echo Password: hafidos159357
echo.
echo ========================================
pause
