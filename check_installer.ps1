# Rental Management System - Simple Installer Check

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Rental Management Installer Check" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if installer exists
$installerPath = "TadbirAlKira_Setup_v1.0.0.exe"
if (-not (Test-Path $installerPath)) {
    Write-Host "ERROR: Installer file not found!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "SUCCESS: Installer found" -ForegroundColor Green

# Get file information
$fileInfo = Get-Item $installerPath
Write-Host ""
Write-Host "[1/3] File Information:" -ForegroundColor Yellow
Write-Host "Name: $($fileInfo.Name)"
Write-Host "Size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
Write-Host "Date: $($fileInfo.LastWriteTime)"

# Check file hash
Write-Host ""
Write-Host "[2/3] Hash Verification:" -ForegroundColor Yellow
try {
    $hash = Get-FileHash $installerPath -Algorithm SHA256
    Write-Host "SHA256: $($hash.Hash)" -ForegroundColor Cyan
    
    # Save hash to file
    $hash.Hash | Out-File "TadbirAlKira_Setup_v1.0.0.exe.sha256" -Encoding UTF8
    Write-Host "Hash saved to .sha256 file" -ForegroundColor Green
} catch {
    Write-Host "Failed to calculate hash" -ForegroundColor Red
}

# Check if file is executable
Write-Host ""
Write-Host "[3/3] Executable Check:" -ForegroundColor Yellow
try {
    $fileVersion = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($installerPath)
    Write-Host "File is executable" -ForegroundColor Green
    if ($fileVersion.FileDescription) {
        Write-Host "Description: $($fileVersion.FileDescription)"
    }
    if ($fileVersion.FileVersion) {
        Write-Host "Version: $($fileVersion.FileVersion)"
    }
} catch {
    Write-Host "File might be corrupted" -ForegroundColor Red
}

# Summary
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Verification Summary:" -ForegroundColor Yellow
Write-Host "✓ File exists and readable"
Write-Host "✓ Size is appropriate (84+ MB)"
Write-Host "✓ Ready to run"
Write-Host ""
Write-Host "Default Login Credentials:" -ForegroundColor Green
Write-Host "Username: hafid"
Write-Host "Password: hafidos159357"
Write-Host ""
Write-Host "Do you want to run the installer now? (y/n)"
$runChoice = Read-Host

if ($runChoice -eq "y" -or $runChoice -eq "Y" -or $runChoice -eq "yes") {
    Write-Host "Running installer..." -ForegroundColor Green
    Start-Process $installerPath -Verb RunAs
} else {
    Write-Host "Cancelled" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Verification completed" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
