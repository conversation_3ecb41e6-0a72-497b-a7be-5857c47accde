@echo off
chcp 65001 > nul
echo ========================================
echo    إنشاء مثبت نظام تدبير الكراء
echo    Creating Rental Management Installer
echo ========================================
echo.

echo [1/4] التحقق من وجود NSIS...
set NSIS_PATH=""
if exist "C:\Program Files (x86)\NSIS\makensis.exe" (
    set NSIS_PATH=C:\Program Files (x86)\NSIS\makensis.exe
) else if exist "C:\Program Files\NSIS\makensis.exe" (
    set NSIS_PATH=C:\Program Files\NSIS\makensis.exe
) else (
    echo ❌ NSIS غير مثبت!
    echo يرجى تحميل وتثبيت NSIS من:
    echo https://nsis.sourceforge.io/Download
    echo.
    pause
    exit /b 1
)

echo ✅ تم العثور على NSIS

echo [2/4] التحقق من الملفات المطلوبة...
if not exist "installer.nsi" (
    echo ❌ ملف installer.nsi غير موجود!
    pause
    exit /b 1
)

if not exist "RentalManagement_Release_v1.0\Portable\SimpleRentalApp.exe" (
    echo ❌ ملف التطبيق غير موجود!
    echo يرجى التأكد من وجود مجلد RentalManagement_Release_v1.0
    pause
    exit /b 1
)

if not exist "app_icon.ico" (
    echo ❌ ملف الأيقونة غير موجود!
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة

echo [3/4] إنشاء المثبت...
"%NSIS_PATH%" installer.nsi

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إنشاء المثبت بنجاح!
    echo.
    echo الملف المُنشأ: TadbirAlKira_Setup_v1.0.0.exe
    echo.
    
    if exist "TadbirAlKira_Setup_v1.0.0.exe" (
        echo [4/4] معلومات الملف:
        for %%A in (TadbirAlKira_Setup_v1.0.0.exe) do echo الحجم: %%~zA bytes
        echo.
        echo هل تريد تشغيل المثبت للاختبار؟ (y/n)
        set /p choice=
        if /i "%choice%"=="y" (
            start TadbirAlKira_Setup_v1.0.0.exe
        )
    )
) else (
    echo ❌ فشل في إنشاء المثبت!
    echo يرجى مراجعة الأخطاء أعلاه
)

echo.
echo ========================================
pause
