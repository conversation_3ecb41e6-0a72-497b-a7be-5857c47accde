@echo off
chcp 65001 > nul
echo ========================================
echo    إنشاء Release لنظام تدبير الكراء
echo    Creating Release for Rental Management System
echo ========================================
echo.

set RELEASE_DIR=RentalManagement_Release_v1.0
set DATE_STR=%date:~6,4%-%date:~3,2%-%date:~0,2%

echo [1/8] إنشاء مجلد Release...
if exist %RELEASE_DIR% rmdir /s /q %RELEASE_DIR%
mkdir %RELEASE_DIR%
mkdir %RELEASE_DIR%\Documentation
mkdir %RELEASE_DIR%\Portable
mkdir %RELEASE_DIR%\Installer_Files

echo [2/8] نسخ التطبيق المحمول...
xcopy publish_release\* %RELEASE_DIR%\Portable\ /E /I /Y > nul

echo [3/8] نسخ الوثائق...
copy DEPLOYMENT_GUIDE.md %RELEASE_DIR%\Documentation\ > nul
copy FINAL_DISTRIBUTION_SUMMARY.md %RELEASE_DIR%\Documentation\ > nul
copy README.txt %RELEASE_DIR%\Documentation\ > nul
copy LICENSE.txt %RELEASE_DIR%\Documentation\ > nul

echo [4/8] نسخ ملفات التثبيت...
copy installer.nsi %RELEASE_DIR%\Installer_Files\ > nul
copy RentalManagement_Final.nsi %RELEASE_DIR%\Installer_Files\ > nul
copy app_icon.ico %RELEASE_DIR%\Installer_Files\ > nul

echo [5/8] إنشاء ملف تشغيل عربي...
echo @echo off > %RELEASE_DIR%\Portable\تشغيل_التطبيق.bat
echo chcp 65001 ^> nul >> %RELEASE_DIR%\Portable\تشغيل_التطبيق.bat
echo echo بدء تشغيل نظام تدبير الكراء... >> %RELEASE_DIR%\Portable\تشغيل_التطبيق.bat
echo echo Starting Rental Management System... >> %RELEASE_DIR%\Portable\تشغيل_التطبيق.bat
echo start SimpleRentalApp.exe >> %RELEASE_DIR%\Portable\تشغيل_التطبيق.bat

echo [6/8] إنشاء ملف معلومات النظام...
echo نظام تدبير الكراء - Rental Management System > %RELEASE_DIR%\معلومات_النظام.txt
echo ================================================ >> %RELEASE_DIR%\معلومات_النظام.txt
echo. >> %RELEASE_DIR%\معلومات_النظام.txt
echo المطور / Developer: حفيظ عبدو / Hafid Abdo >> %RELEASE_DIR%\معلومات_النظام.txt
echo الإصدار / Version: 1.0.0 >> %RELEASE_DIR%\معلومات_النظام.txt
echo تاريخ الإصدار / Release Date: %DATE_STR% >> %RELEASE_DIR%\معلومات_النظام.txt
echo. >> %RELEASE_DIR%\معلومات_النظام.txt
echo بيانات تسجيل الدخول الافتراضية / Default Login: >> %RELEASE_DIR%\معلومات_النظام.txt
echo اسم المستخدم / Username: hafid >> %RELEASE_DIR%\معلومات_النظام.txt
echo كلمة المرور / Password: hafidos159357 >> %RELEASE_DIR%\معلومات_النظام.txt
echo. >> %RELEASE_DIR%\معلومات_النظام.txt
echo الميزات الرئيسية / Main Features: >> %RELEASE_DIR%\معلومات_النظام.txt
echo - إدارة العقارات والمحلات / Property Management >> %RELEASE_DIR%\معلومات_النظام.txt
echo - تتبع عقود الإيجار / Rental Contract Tracking >> %RELEASE_DIR%\معلومات_النظام.txt
echo - إدارة المدفوعات والإيصالات / Payment Management >> %RELEASE_DIR%\معلومات_النظام.txt
echo - ضريبة النظافة / Cleaning Tax >> %RELEASE_DIR%\معلومات_النظام.txt
echo - إشعارات WhatsApp / WhatsApp Notifications >> %RELEASE_DIR%\معلومات_النظام.txt
echo - تقارير PDF / PDF Reports >> %RELEASE_DIR%\معلومات_النظام.txt
echo - النسخ الاحتياطي / Backup System >> %RELEASE_DIR%\معلومات_النظام.txt

echo [7/8] إنشاء دليل التثبيت السريع...
echo دليل التثبيت السريع - Quick Installation Guide > %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo ================================================ >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo. >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo للتشغيل المباشر / Direct Run: >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo 1. افتح مجلد Portable >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo 2. شغل ملف تشغيل_التطبيق.bat أو SimpleRentalApp.exe >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo. >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo لإنشاء ملف تثبيت / Create Installer: >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo 1. تثبيت NSIS من https://nsis.sourceforge.io/ >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo 2. افتح ملف installer.nsi من مجلد Installer_Files >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo 3. اضغط Compile NSI >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo. >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo متطلبات النظام / System Requirements: >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo - Windows 10 أو أحدث / Windows 10 or newer >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo - 4 GB RAM (الحد الأدنى / minimum) >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt
echo - 200 MB مساحة فارغة / free space >> %RELEASE_DIR%\دليل_التثبيت_السريع.txt

echo [8/8] إنشاء ملف ZIP للتوزيع...
powershell -command "Compress-Archive -Path '%RELEASE_DIR%\*' -DestinationPath 'RentalManagement_Release_v1.0_%DATE_STR%.zip' -Force"

echo.
echo ========================================
echo تم إنشاء Release بنجاح!
echo Release created successfully!
echo.
echo الملفات المتاحة / Available Files:
echo - مجلد Release: %RELEASE_DIR%
echo - ملف ZIP: RentalManagement_Release_v1.0_%DATE_STR%.zip
echo.
echo حجم التطبيق / Application Size:
for %%A in (%RELEASE_DIR%\Portable\SimpleRentalApp.exe) do echo - التطبيق الرئيسي: %%~zA bytes
echo ========================================
echo.
pause
