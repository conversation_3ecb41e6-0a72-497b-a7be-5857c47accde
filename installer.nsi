; نظام تدبير الكراء - Rental Management System - NSIS Installer
; Developer: حفيظ عبدو - <PERSON><PERSON><PERSON>
; Arabic/English Version

Unicode true

!define APP_NAME "Rental Management System"
!define APP_NAME_EN "Rental Management System"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Hafid Abdou"
!define APP_DESCRIPTION "Property and Rental Management System"
!define APP_DESCRIPTION_EN "Property Rental Management Application"
!define APP_EXE "SimpleRentalApp.exe"
!define SOURCE_DIR "RentalManagement_Release_v1.0\Portable"

; Include required libraries
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "WinVer.nsh"

; General settings
Name "${APP_NAME_EN}"
OutFile "TadbirAlKira_Setup_v${APP_VERSION}.exe"
InstallDir "$PROGRAMFILES64\TadbirAlKira"
InstallDirRegKey HKLM "Software\TadbirAlKira" "InstallDir"
RequestExecutionLevel admin

; Modern UI settings
!define MUI_ABORTWARNING
!define MUI_ICON "app_icon.ico"
!define MUI_UNICON "app_icon.ico"

; Welcome page customization
!define MUI_WELCOMEPAGE_TITLE "Welcome to setup ${APP_NAME}"
!define MUI_WELCOMEPAGE_TEXT "This wizard will guide you through the installation of  ${APP_NAME} in your computer.$\r$\n$\r$\nThis professional program help you to manage your Business rental.$\r$\n$\r$\n click on next for continue"

; Finish page customization
!define MUI_FINISHPAGE_TITLE "Finished"
!define MUI_FINISHPAGE_TEXT "Installed ${APP_NAME} successfuly.$\r$\n$\r$\nYou can now start managing your properties..$\r$\n$\r$\nClick Finish to exit the installation.."
!define MUI_FINISHPAGE_RUN "$INSTDIR\${APP_EXE}"
!define MUI_FINISHPAGE_RUN_TEXT "run ${APP_NAME} now"

; Installer pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Language
!insertmacro MUI_LANGUAGE "English"

; Version information
VIProductVersion "${APP_VERSION}.0"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductName" "${APP_NAME_EN}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "Comments" "${APP_DESCRIPTION_EN}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "LegalCopyright" "© 2025 ${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileDescription" "${APP_DESCRIPTION_EN}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_ENGLISH} "ProductVersion" "${APP_VERSION}"

; Main installation section
Section "Main application" SecMain
  SectionIn RO

  SetOutPath "$INSTDIR"

  DetailPrint "Install application files..."

  ; Copy main executable and all files
  File "${SOURCE_DIR}\${APP_EXE}"
  File "${SOURCE_DIR}\rental_management.db"

  ; Copy font folder
  SetOutPath "$INSTDIR\LatoFont"
  File "${SOURCE_DIR}\LatoFont\*.*"

  ; Copy documentation
  SetOutPath "$INSTDIR"
  File "README.txt"
  File "LICENSE.txt"
  
  DetailPrint "Create shortcuts..."

  ; Create Start Menu folder and shortcuts
  CreateDirectory "$SMPROGRAMS\${APP_NAME}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0 SW_SHOWNORMAL "" "${APP_DESCRIPTION}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\Uninstall.lnk" "$INSTDIR\Uninstall.exe" "" "$INSTDIR\Uninstall.exe" 0 SW_SHOWNORMAL "" "Uninstall ${APP_NAME}"
  CreateShortCut "$SMPROGRAMS\${APP_NAME}\User Guide.lnk" "$INSTDIR\README.txt" "" "" 0 SW_SHOWNORMAL "" "User Guide"

  ; Create Desktop shortcut
  CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0 SW_SHOWNORMAL "" "${APP_DESCRIPTION}"
  
  DetailPrint "Application registration..."

  ; Registry entries for Add/Remove Programs
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "DisplayName" "${APP_NAME_EN}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "UninstallString" "$INSTDIR\Uninstall.exe"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "InstallLocation" "$INSTDIR"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "DisplayIcon" "$INSTDIR\${APP_EXE}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "Publisher" "${APP_PUBLISHER}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "DisplayVersion" "${APP_VERSION}"
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "Comments" "${APP_DESCRIPTION_EN}"
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "NoModify" 1
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "NoRepair" 1
  
  ; Calculate installation size
  ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
  IntFmt $0 "0x%08X" $0
  WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira" "EstimatedSize" "$0"

  ; Create uninstaller
  DetailPrint "Create an uninstaller..."
  WriteUninstaller "$INSTDIR\Uninstall.exe"

  ; Save installation directory
  WriteRegStr HKLM "Software\TadbirAlKira" "InstallDir" "$INSTDIR"

  ; Create batch file for easy launch
  FileOpen $0 "$INSTDIR\Launch_App.bat" w
  FileWrite $0 "@echo off$\r$\n"
  FileWrite $0 "chcp 65001 > nul$\r$\n"
  FileWrite $0 "echo Starting Rental Management System...$\r$\n"
  FileWrite $0 "start SimpleRentalApp.exe$\r$\n"
  FileClose $0

  DetailPrint "Installation completed successfully!"
SectionEnd

; Uninstaller section
Section "Uninstall"
  DetailPrint "Remove application files..."

  ; Ask user about keeping data
  MessageBox MB_YESNO "Do you want to keep application data (database and settings)?$\r$\n$\r$\nSelect 'No' to permanently delete all data.." IDYES KeepData

  ; Delete user data if requested
  RMDir /r "$APPDATA\TadbirAlKira"
  RMDir /r "$DOCUMENTS\TadbirAlKira_Backups"
  RMDir /r "$DOCUMENTS\TadbirAlKira_Receipts"

  KeepData:

  ; Delete main files
  Delete "$INSTDIR\${APP_EXE}"
  Delete "$INSTDIR\rental_management.db"
  Delete "$INSTDIR\README.txt"
  Delete "$INSTDIR\LICENSE.txt"
  Delete "$INSTDIR\Launch_App.bat"
  Delete "$INSTDIR\Uninstall.exe"

  ; Delete font folder
  RMDir /r "$INSTDIR\LatoFont"

  DetailPrint "Remove shortcuts..."

  ; Delete shortcuts
  Delete "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk"
  Delete "$SMPROGRAMS\${APP_NAME}\Uninstall.lnk"
  Delete "$SMPROGRAMS\${APP_NAME}\User Guide.lnk"
  Delete "$DESKTOP\${APP_NAME}.lnk"

  ; Remove directories
  RMDir "$SMPROGRAMS\${APP_NAME}"
  RMDir "$INSTDIR"

  DetailPrint "Remove registry entries..."

  ; Delete registry keys
  DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\TadbirAlKira"
  DeleteRegKey HKLM "Software\TadbirAlKira"

  DetailPrint "Uninstallation completed successfully.!"
SectionEnd

; Functions
Function .onInit
  ; Check if already installed
  ReadRegStr $R0 HKLM "Software\TadbirAlKira" "InstallDir"
  StrCmp $R0 "" NotInstalled
  MessageBox MB_YESNO "It seems that ${APP_NAME} Already installed in:$\r$\n$R0$\r$\n$\r$\nهDo you want to continue and overwrite the current installation?" IDYES NotInstalled
  Abort

  NotInstalled:
  ; Check Windows version
  ${IfNot} ${AtLeastWin10}
    MessageBox MB_OK "This app requires Windows 10 or later..$\r$\nThis version cannot be continued. Windows."
    Abort
  ${EndIf}
FunctionEnd

Function .onInstSuccess
  ; Success message with option to launch
  MessageBox MB_YESNO "Installed ${APP_NAME} Successfully!$\r$\n$\r$\nDefault login data:$\r$\nuser name: hafid$\r$\npassword: ask dev$\r$\n$\r$\nهDo you want to run the app now?" IDNO +2
  ExecShell "open" "$INSTDIR\${APP_EXE}"
FunctionEnd

Function un.onInit
  MessageBox MB_YESNO "Are you sure you want to uninstall? ${APP_NAME}؟" IDYES +2
  Abort
FunctionEnd
