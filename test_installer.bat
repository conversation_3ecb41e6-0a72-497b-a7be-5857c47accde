@echo off
chcp 65001 > nul
echo ========================================
echo    اختبار مثبت نظام تدبير الكراء
echo    Testing Rental Management Installer
echo ========================================
echo.

echo [1/3] التحقق من وجود المثبت...
if not exist "TadbirAlKira_Setup_v1.0.0.exe" (
    echo ❌ ملف المثبت غير موجود!
    echo يرجى التأكد من وجود TadbirAlKira_Setup_v1.0.0.exe
    pause
    exit /b 1
)

echo ✅ تم العثور على المثبت

echo [2/3] معلومات الملف:
for %%A in (TadbirAlKira_Setup_v1.0.0.exe) do (
    echo الحجم: %%~zA bytes
    echo التاريخ: %%~tA
)

echo.
echo [3/3] هل تريد تشغيل المثبت للاختبار؟
echo ⚠️  تحذير: سيتم تثبيت التطبيق فعلياً على النظام
echo.
echo اختر:
echo [1] تشغيل المثبت
echo [2] فتح مجلد المثبت فقط
echo [3] إلغاء
echo.
set /p choice="اختيارك (1-3): "

if "%choice%"=="1" (
    echo تشغيل المثبت...
    start TadbirAlKira_Setup_v1.0.0.exe
    echo تم تشغيل المثبت. اتبع التعليمات على الشاشة.
) else if "%choice%"=="2" (
    echo فتح مجلد المثبت...
    explorer TadbirAlKira_Installer_Package
) else (
    echo تم الإلغاء.
)

echo.
echo ========================================
pause
