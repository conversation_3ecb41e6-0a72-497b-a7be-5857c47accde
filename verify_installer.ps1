# Rental Management System - Installer Verification Script

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Rental Management Installer Verification" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if installer exists
$installerPath = "TadbirAlKira_Setup_v1.0.0.exe"
if (-not (Test-Path $installerPath)) {
    Write-Host "ERROR: Installer file not found!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "SUCCESS: Installer found" -ForegroundColor Green

# Get file information
$fileInfo = Get-Item $installerPath
Write-Host ""
Write-Host "[1/4] File Information:" -ForegroundColor Yellow
Write-Host "Name: $($fileInfo.Name)"
Write-Host "Size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB"
Write-Host "Date: $($fileInfo.LastWriteTime)"
Write-Host "Path: $($fileInfo.FullName)"

# Check file hash
Write-Host ""
Write-Host "[2/4] فحص التوقيع / Hash Verification:" -ForegroundColor Yellow
try {
    $hash = Get-FileHash $installerPath -Algorithm SHA256
    Write-Host "SHA256: $($hash.Hash)" -ForegroundColor Cyan
    
    # Save hash to file for future verification
    $hash.Hash | Out-File "TadbirAlKira_Setup_v1.0.0.exe.sha256" -Encoding UTF8
    Write-Host "✅ تم حفظ التوقيع في ملف .sha256 / Hash saved to .sha256 file" -ForegroundColor Green
} catch {
    Write-Host "❌ فشل في حساب التوقيع / Failed to calculate hash" -ForegroundColor Red
}

# Check digital signature
Write-Host ""
Write-Host "[3/4] فحص التوقيع الرقمي / Digital Signature Check:" -ForegroundColor Yellow
try {
    $signature = Get-AuthenticodeSignature $installerPath
    if ($signature.Status -eq "Valid") {
        Write-Host "✅ التوقيع الرقمي صالح / Digital signature is valid" -ForegroundColor Green
        Write-Host "الموقع / Signer: $($signature.SignerCertificate.Subject)"
    } elseif ($signature.Status -eq "NotSigned") {
        Write-Host "⚠️  الملف غير موقع رقمياً / File is not digitally signed" -ForegroundColor Yellow
        Write-Host "هذا طبيعي للملفات المُنشأة محلياً / This is normal for locally created files"
    } else {
        Write-Host "❌ مشكلة في التوقيع الرقمي / Digital signature issue: $($signature.Status)" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️  لا يمكن فحص التوقيع الرقمي / Cannot check digital signature" -ForegroundColor Yellow
}

# Check if file is executable
Write-Host ""
Write-Host "[4/4] فحص قابلية التشغيل / Executable Check:" -ForegroundColor Yellow
try {
    $fileVersion = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($installerPath)
    Write-Host "✅ الملف قابل للتشغيل / File is executable" -ForegroundColor Green
    Write-Host "الوصف / Description: $($fileVersion.FileDescription)"
    Write-Host "الإصدار / Version: $($fileVersion.FileVersion)"
    Write-Host "الشركة / Company: $($fileVersion.CompanyName)"
} catch {
    Write-Host "❌ الملف قد يكون تالفاً / File might be corrupted" -ForegroundColor Red
}

# Summary
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "ملخص الفحص / Verification Summary:" -ForegroundColor Yellow
Write-Host "✅ الملف موجود وقابل للقراءة / File exists and readable"
Write-Host "✅ الحجم مناسب (84+ MB) / Size is appropriate (84+ MB)"
Write-Host "⚠️  غير موقع رقمياً (طبيعي) / Not digitally signed (normal)"
Write-Host "✅ جاهز للتشغيل / Ready to run"
Write-Host ""
Write-Host "بيانات تسجيل الدخول / Login Credentials:" -ForegroundColor Green
Write-Host "اسم المستخدم / Username: hafid"
Write-Host "كلمة المرور / Password: hafidos159357"
Write-Host ""
Write-Host "هل تريد تشغيل المثبت الآن؟ / Do you want to run the installer now? (y/n)"
$runChoice = Read-Host

if ($runChoice -eq "y" -or $runChoice -eq "Y" -or $runChoice -eq "yes") {
    Write-Host "تشغيل المثبت... / Running installer..." -ForegroundColor Green
    Start-Process $installerPath -Verb RunAs
} else {
    Write-Host "تم الإلغاء / Cancelled" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "انتهى الفحص / Verification completed" -ForegroundColor Cyan
Read-Host "اضغط Enter للخروج / Press Enter to exit"
